package com.logictrue.controller;

import com.logictrue.service.DatabaseService;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel数据详情对话框控制器
 */
public class ExcelDataDetailDialogController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(ExcelDataDetailDialogController.class);

    @FXML
    private Button closeButton;

    @FXML
    private TabPane tabPane;

    // 基本信息控件
    @FXML
    private Label recordIdLabel;
    @FXML
    private Label deviceCodeLabel;
    @FXML
    private Label templateNameLabel;
    @FXML
    private Label fileNameLabel;
    @FXML
    private Label filePathLabel;
    @FXML
    private Label fileSizeLabel;
    @FXML
    private Label parseStatusLabel;
    @FXML
    private Label parseTimeLabel;
    @FXML
    private Label createTimeLabel;
    @FXML
    private Label totalSheetsLabel;
    @FXML
    private Label parsedSheetsLabel;
    @FXML
    private Label basicFieldsCountLabel;
    @FXML
    private Label tableRowsCountLabel;
    @FXML
    private Label parseMessageLabel;
    @FXML
    private Label remarkLabel;

    // 基础字段控件
    @FXML
    private ComboBox<String> basicFieldSheetComboBox;
    @FXML
    private TableView<DatabaseService.BasicFieldData> basicFieldsTable;
    @FXML
    private TableColumn<DatabaseService.BasicFieldData, String> fieldCodeColumn;
    @FXML
    private TableColumn<DatabaseService.BasicFieldData, String> fieldNameColumn;
    @FXML
    private TableColumn<DatabaseService.BasicFieldData, String> fieldValueColumn;
    @FXML
    private TableColumn<DatabaseService.BasicFieldData, String> fieldTypeColumn;
    @FXML
    private TableColumn<DatabaseService.BasicFieldData, String> fieldPositionColumn;

    // 表格数据控件
    @FXML
    private ComboBox<String> tableDataSheetComboBox;
    @FXML
    private TableView<Map<String, String>> tableDataTable;
    @FXML
    private Label tableDataCountLabel;

    // 详情数据控件
    @FXML
    private ComboBox<String> detailDataSheetComboBox;
    @FXML
    private VBox basicFieldsSection;
    @FXML
    private VBox basicFieldsContent;
    @FXML
    private VBox tableDataSection;
    @FXML
    private TableView<Map<String, String>> detailTableDataTable;
    @FXML
    private Label detailTableDataCountLabel;

    private DatabaseService databaseService;
    private DatabaseService.ExcelDataDetail dataDetail;
    private ObservableList<DatabaseService.BasicFieldData> basicFieldsList;
    private ObservableList<Map<String, String>> tableDataList;
    private ObservableList<Map<String, String>> detailTableDataList;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        databaseService = DatabaseService.getInstance();
        basicFieldsList = FXCollections.observableArrayList();
        tableDataList = FXCollections.observableArrayList();
        detailTableDataList = FXCollections.observableArrayList();

        initializeBasicFieldsTable();
        initializeEventHandlers();

        logger.info("Excel数据详情对话框控制器初始化完成");
    }

    /**
     * 初始化基础字段表格
     */
    private void initializeBasicFieldsTable() {
        fieldCodeColumn.setCellValueFactory(new PropertyValueFactory<>("fieldCode"));
        fieldNameColumn.setCellValueFactory(new PropertyValueFactory<>("fieldName"));
        fieldValueColumn.setCellValueFactory(new PropertyValueFactory<>("fieldValue"));
        fieldTypeColumn.setCellValueFactory(new PropertyValueFactory<>("fieldType"));

        fieldPositionColumn.setCellValueFactory(cellData -> {
            DatabaseService.BasicFieldData field = cellData.getValue();
            String position = "";
            if (field.getRowIndex() != null && field.getColIndex() != null) {
                position = "R" + field.getRowIndex() + "C" + field.getColIndex();
            }
            return new SimpleStringProperty(position);
        });

        // 设置表格居中对齐
        fieldCodeColumn.setCellFactory(column -> createCenteredCell());
        fieldNameColumn.setCellFactory(column -> createCenteredCell());
        fieldValueColumn.setCellFactory(column -> createCenteredCell());
        fieldTypeColumn.setCellFactory(column -> createCenteredCell());
        fieldPositionColumn.setCellFactory(column -> createCenteredCell());

        basicFieldsTable.setItems(basicFieldsList);
    }

    /**
     * 初始化事件处理器
     */
    private void initializeEventHandlers() {
        closeButton.setOnAction(event -> closeDialog());

        // 基础字段Sheet选择事件
        basicFieldSheetComboBox.setOnAction(event -> filterBasicFieldsBySheet());

        // 表格数据Sheet选择事件
        tableDataSheetComboBox.setOnAction(event -> filterTableDataBySheet());

        // 详情数据Sheet选择事件
        detailDataSheetComboBox.setOnAction(event -> filterDetailDataBySheet());
    }

    /**
     * 创建居中对齐的单元格
     */
    private <T> TableCell<T, String> createCenteredCell() {
        return new TableCell<T, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item);
                }
                setAlignment(Pos.CENTER);
            }
        };
    }

    /**
     * 设置数据详情
     */
    public void setDataDetail(DatabaseService.ExcelDataDetail detail) {
        this.dataDetail = detail;
        if (detail != null) {
            displayBasicInfo(detail.getMainRecord());
            setupBasicFields(detail.getBasicFields());
            setupTableData(detail.getTableData());
            setupDetailData(detail.getBasicFields(), detail.getTableData());
        }
    }

    /**
     * 显示基本信息
     */
    private void displayBasicInfo(DatabaseService.ExcelDataRecord record) {
        if (record == null) return;

        recordIdLabel.setText(record.getId() != null ? record.getId().toString() : "");
        deviceCodeLabel.setText(record.getDeviceCode() != null ? record.getDeviceCode() : "");
        templateNameLabel.setText(record.getTemplateName() != null ? record.getTemplateName() : "");
        fileNameLabel.setText(record.getFileName() != null ? record.getFileName() : "");
        filePathLabel.setText(record.getFilePath() != null ? record.getFilePath() : "");
        fileSizeLabel.setText(formatFileSize(record.getFileSize() != null ? record.getFileSize() : 0));
        parseStatusLabel.setText(record.getParseStatusText());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        parseTimeLabel.setText(record.getParseTime() != null ? record.getParseTime().format(formatter) : "");
        createTimeLabel.setText(record.getCreateTime() != null ? record.getCreateTime().format(formatter) : "");

        totalSheetsLabel.setText(record.getTotalSheets() != null ? record.getTotalSheets().toString() : "0");
        parsedSheetsLabel.setText(record.getParsedSheets() != null ? record.getParsedSheets().toString() : "0");
        basicFieldsCountLabel.setText(record.getBasicFieldsCount() != null ? record.getBasicFieldsCount().toString() : "0");
        tableRowsCountLabel.setText(record.getTableRowsCount() != null ? record.getTableRowsCount().toString() : "0");

        parseMessageLabel.setText(record.getParseMessage() != null ? record.getParseMessage() : "");
        remarkLabel.setText(record.getRemark() != null ? record.getRemark() : "");
    }

    /**
     * 设置基础字段数据
     */
    private void setupBasicFields(List<DatabaseService.BasicFieldData> basicFields) {
        if (basicFields == null || basicFields.isEmpty()) {
            return;
        }

        // 获取所有Sheet
        Set<String> sheets = basicFields.stream()
                .map(DatabaseService.BasicFieldData::getSheetName)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(LinkedHashSet::new));

        basicFieldSheetComboBox.getItems().clear();
        basicFieldSheetComboBox.getItems().add("全部");
        basicFieldSheetComboBox.getItems().addAll(sheets);
        basicFieldSheetComboBox.setValue("全部");

        // 显示所有基础字段
        basicFieldsList.clear();
        basicFieldsList.addAll(basicFields);
    }

    /**
     * 设置表格数据
     */
    private void setupTableData(List<DatabaseService.TableDataInfo> tableData) {
        if (tableData == null || tableData.isEmpty()) {
            return;
        }

        // 获取所有Sheet
        Set<String> sheets = tableData.stream()
                .map(DatabaseService.TableDataInfo::getSheetName)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(LinkedHashSet::new));

        tableDataSheetComboBox.getItems().clear();
        tableDataSheetComboBox.getItems().addAll(sheets);

        if (!sheets.isEmpty()) {
            tableDataSheetComboBox.setValue(sheets.iterator().next());
            filterTableDataBySheet();
        }
    }

    /**
     * 按Sheet筛选基础字段
     */
    private void filterBasicFieldsBySheet() {
        if (dataDetail == null || dataDetail.getBasicFields() == null) {
            return;
        }

        String selectedSheet = basicFieldSheetComboBox.getValue();
        if (selectedSheet == null) {
            return;
        }

        basicFieldsList.clear();

        if ("全部".equals(selectedSheet)) {
            basicFieldsList.addAll(dataDetail.getBasicFields());
        } else {
            List<DatabaseService.BasicFieldData> filtered = dataDetail.getBasicFields().stream()
                    .filter(field -> selectedSheet.equals(field.getSheetName()))
                    .collect(Collectors.toList());
            basicFieldsList.addAll(filtered);
        }
    }

    /**
     * 按Sheet筛选表格数据
     */
    private void filterTableDataBySheet() {
        if (dataDetail == null || dataDetail.getTableData() == null) {
            return;
        }

        String selectedSheet = tableDataSheetComboBox.getValue();
        if (selectedSheet == null) {
            return;
        }

        // 筛选当前Sheet的数据
        List<DatabaseService.TableDataInfo> sheetData = dataDetail.getTableData().stream()
                .filter(data -> selectedSheet.equals(data.getSheetName()))
                .collect(Collectors.toList());

        if (sheetData.isEmpty()) {
            tableDataList.clear();
            tableDataCountLabel.setText("共 0 行数据");
            return;
        }

        // 获取所有字段编码（表头）
        Set<String> fieldCodes = sheetData.stream()
                .map(DatabaseService.TableDataInfo::getFieldCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(LinkedHashSet::new));

        // 按行索引分组数据
        Map<Integer, List<DatabaseService.TableDataInfo>> rowDataMap = sheetData.stream()
                .collect(Collectors.groupingBy(DatabaseService.TableDataInfo::getRowIndex));

        // 清空现有列
        tableDataTable.getColumns().clear();

        // 添加行号列
        TableColumn<Map<String, String>, String> rowNumberColumn = new TableColumn<>("行号");
        rowNumberColumn.setPrefWidth(60);
        rowNumberColumn.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().get("_rowNumber")));
        rowNumberColumn.setCellFactory(column -> createCenteredCell());
        tableDataTable.getColumns().add(rowNumberColumn);

        // 动态创建列
        for (String fieldCode : fieldCodes) {
            TableColumn<Map<String, String>, String> column = new TableColumn<>(fieldCode);
            column.setPrefWidth(120);
            column.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().get(fieldCode)));
            column.setCellFactory(col -> createCenteredCell());
            tableDataTable.getColumns().add(column);
        }

        // 构建表格数据
        tableDataList.clear();
        List<Integer> sortedRows = new ArrayList<>(rowDataMap.keySet());
        sortedRows.sort(Integer::compareTo);

        for (int i = 0; i < sortedRows.size(); i++) {
            Integer rowIndex = sortedRows.get(i);
            List<DatabaseService.TableDataInfo> rowData = rowDataMap.get(rowIndex);

            Map<String, String> rowMap = new HashMap<>();
            rowMap.put("_rowNumber", String.valueOf(i + 1)); // 显示行号从1开始

            for (DatabaseService.TableDataInfo data : rowData) {
                rowMap.put(data.getFieldCode(), data.getFieldValue() != null ? data.getFieldValue() : "");
            }

            tableDataList.add(rowMap);
        }

        tableDataTable.setItems(tableDataList);
        tableDataCountLabel.setText("共 " + tableDataList.size() + " 行数据");
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 设置详情数据
     */
    private void setupDetailData(List<DatabaseService.BasicFieldData> basicFields, List<DatabaseService.TableDataInfo> tableData) {
        // 获取所有Sheet
        Set<String> sheets = new LinkedHashSet<>();

        if (basicFields != null) {
            basicFields.stream()
                    .map(DatabaseService.BasicFieldData::getSheetName)
                    .filter(Objects::nonNull)
                    .forEach(sheets::add);
        }

        if (tableData != null) {
            tableData.stream()
                    .map(DatabaseService.TableDataInfo::getSheetName)
                    .filter(Objects::nonNull)
                    .forEach(sheets::add);
        }

        detailDataSheetComboBox.getItems().clear();
        detailDataSheetComboBox.getItems().addAll(sheets);

        if (!sheets.isEmpty()) {
            detailDataSheetComboBox.setValue(sheets.iterator().next());
            filterDetailDataBySheet();
        }
    }

    /**
     * 按Sheet筛选详情数据
     */
    private void filterDetailDataBySheet() {
        if (dataDetail == null) {
            return;
        }

        String selectedSheet = detailDataSheetComboBox.getValue();
        if (selectedSheet == null) {
            return;
        }

        // 显示基础字段
        displayDetailBasicFields(selectedSheet);

        // 显示表格数据
        displayDetailTableData(selectedSheet);
    }

    /**
     * 显示详情基础字段
     */
    private void displayDetailBasicFields(String sheetName) {
        basicFieldsContent.getChildren().clear();

        if (dataDetail.getBasicFields() == null || dataDetail.getBasicFields().isEmpty()) {
            basicFieldsSection.setVisible(false);
            basicFieldsSection.setManaged(false);
            return;
        }

        // 筛选当前Sheet的基础字段
        List<DatabaseService.BasicFieldData> sheetBasicFields = dataDetail.getBasicFields().stream()
                .filter(field -> sheetName.equals(field.getSheetName()))
                .collect(Collectors.toList());

        if (sheetBasicFields.isEmpty()) {
            basicFieldsSection.setVisible(false);
            basicFieldsSection.setManaged(false);
            return;
        }

        basicFieldsSection.setVisible(true);
        basicFieldsSection.setManaged(true);

        // 创建基础字段显示
        for (DatabaseService.BasicFieldData field : sheetBasicFields) {
            HBox fieldBox = new HBox(10);
            fieldBox.setAlignment(Pos.CENTER_LEFT);

            // 字段名称（加粗）
            Label nameLabel = new Label("• " + (field.getFieldName() != null ? field.getFieldName() : field.getFieldCode()) + ":");
            nameLabel.setStyle("-fx-font-weight: bold;");
            nameLabel.setPrefWidth(150);

            // 字段值
            Label valueLabel = new Label(field.getFieldValue() != null ? field.getFieldValue() : "");
            valueLabel.setStyle("-fx-text-fill: #333333;");

            // 位置信息
            String position = "";
            if (field.getRowIndex() != null && field.getColIndex() != null) {
                position = " (R" + field.getRowIndex() + "C" + field.getColIndex() + ")";
            }
            Label positionLabel = new Label(position);
            positionLabel.setStyle("-fx-text-fill: #666666; -fx-font-size: 11px;");

            fieldBox.getChildren().addAll(nameLabel, valueLabel, positionLabel);
            basicFieldsContent.getChildren().add(fieldBox);
        }
    }

    /**
     * 显示详情表格数据
     */
    private void displayDetailTableData(String sheetName) {
        if (dataDetail.getTableData() == null || dataDetail.getTableData().isEmpty()) {
            tableDataSection.setVisible(false);
            tableDataSection.setManaged(false);
            return;
        }

        // 筛选当前Sheet的数据
        List<DatabaseService.TableDataInfo> sheetData = dataDetail.getTableData().stream()
                .filter(data -> sheetName.equals(data.getSheetName()))
                .collect(Collectors.toList());

        if (sheetData.isEmpty()) {
            tableDataSection.setVisible(false);
            tableDataSection.setManaged(false);
            detailTableDataCountLabel.setText("共 0 行数据");
            return;
        }

        tableDataSection.setVisible(true);
        tableDataSection.setManaged(true);

        // 获取所有字段编码（表头）
        Set<String> fieldCodes = sheetData.stream()
                .map(DatabaseService.TableDataInfo::getFieldCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(LinkedHashSet::new));

        // 按行索引分组数据
        Map<Integer, List<DatabaseService.TableDataInfo>> rowDataMap = sheetData.stream()
                .collect(Collectors.groupingBy(DatabaseService.TableDataInfo::getRowIndex));

        // 清空现有列
        detailTableDataTable.getColumns().clear();

        // 添加序号列
        TableColumn<Map<String, String>, String> rowNumberColumn = new TableColumn<>("序号");
        rowNumberColumn.setPrefWidth(60);
        rowNumberColumn.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().get("_rowNumber")));
        rowNumberColumn.setCellFactory(column -> createCenteredCell());
        detailTableDataTable.getColumns().add(rowNumberColumn);

        // 动态创建列，显示位置信息
        for (String fieldCode : fieldCodes) {
            // 获取该字段的位置信息
            String columnHeader = fieldCode;
            Optional<DatabaseService.TableDataInfo> firstData = sheetData.stream()
                    .filter(data -> fieldCode.equals(data.getFieldCode()))
                    .findFirst();

            if (firstData.isPresent() && firstData.get().getColIndex() != null) {
                columnHeader = fieldCode + "(C" + firstData.get().getColIndex() + ")";
            }

            TableColumn<Map<String, String>, String> column = new TableColumn<>(columnHeader);
            column.setPrefWidth(120);
            column.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().get(fieldCode)));
            column.setCellFactory(col -> createCenteredCell());
            detailTableDataTable.getColumns().add(column);
        }

        // 构建表格数据
        detailTableDataList.clear();
        List<Integer> sortedRows = new ArrayList<>(rowDataMap.keySet());
        sortedRows.sort(Integer::compareTo);

        for (int i = 0; i < sortedRows.size(); i++) {
            Integer rowIndex = sortedRows.get(i);
            List<DatabaseService.TableDataInfo> rowData = rowDataMap.get(rowIndex);

            Map<String, String> rowMap = new HashMap<>();
            rowMap.put("_rowNumber", String.valueOf(i + 1)); // 显示序号从1开始

            for (DatabaseService.TableDataInfo data : rowData) {
                rowMap.put(data.getFieldCode(), data.getFieldValue() != null ? data.getFieldValue() : "");
            }

            detailTableDataList.add(rowMap);
        }

        detailTableDataTable.setItems(detailTableDataList);
        detailTableDataCountLabel.setText("共 " + detailTableDataList.size() + " 行数据");
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) closeButton.getScene().getWindow();
        stage.close();
    }
}
