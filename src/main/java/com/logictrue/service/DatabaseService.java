package com.logictrue.service;

import com.logictrue.model.DataRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据库服务类
 * 实现SQLite数据库连接、表创建和分页查询功能
 */
public class DatabaseService {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);

    private static final String DB_NAME = "data_records.db";
    private static final String TABLE_NAME = "data_records";

    // Excel数据相关表名
    private static final String DEVICE_DETECTION_DATA_TABLE = "device_detection_data";
    private static final String DEVICE_DETECTION_BASIC_FIELD_TABLE = "device_detection_basic_field";
    private static final String DEVICE_DETECTION_TABLE_HEADER_TABLE = "device_detection_table_header";
    private static final String DEVICE_DETECTION_TABLE_DATA_TABLE = "device_detection_table_data";

    private static DatabaseService instance;
    private String dbPath;

    private DatabaseService() {
        // 获取jar包同级目录
        String jarDir = getJarDirectory();
        this.dbPath = jarDir + File.separator + DB_NAME;
        initializeDatabase();
    }

    /**
     * 获取单例实例
     */
    public static synchronized DatabaseService getInstance() {
        if (instance == null) {
            instance = new DatabaseService();
        }
        return instance;
    }

    /**
     * 获取jar包所在目录
     */
    private String getJarDirectory() {
        try {
            String jarPath = DatabaseService.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(jarPath);

            if (jarFile.isFile()) {
                // 运行的是jar包
                return jarFile.getParent();
            } else {
                // 开发环境，使用项目根目录
                return System.getProperty("user.dir");
            }
        } catch (Exception e) {
            logger.error("获取jar包目录失败", e);
            return System.getProperty("user.dir");
        }
    }

    /**
     * 初始化数据库
     */
    private void initializeDatabase() {
        try (Connection conn = getConnection()) {
            createTableIfNotExists(conn);
            createExcelDataTablesIfNotExists(conn);
            logger.info("数据库初始化成功，数据库路径: {}", dbPath);
        } catch (SQLException e) {
            logger.error("数据库初始化失败", e);
        }
    }

    /**
     * 获取数据库连接
     */
    private Connection getConnection() throws SQLException {
        String url = "jdbc:sqlite:" + dbPath;
        return DriverManager.getConnection(url);
    }

    /**
     * 创建数据表（如果不存在）
     */
    private void createTableIfNotExists(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "file_name TEXT NOT NULL," +
                "collect_time TIMESTAMP NOT NULL," +
                "file_path TEXT NOT NULL" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("数据表创建成功: {}", TABLE_NAME);
        }
    }

    /**
     * 插入数据记录
     */
    public boolean insertRecord(String fileName, LocalDateTime collectTime, String filePath) {
        String sql = "INSERT INTO " + TABLE_NAME + " (file_name, collect_time, file_path) VALUES (?, ?, ?)";

        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, fileName);
            pstmt.setTimestamp(2, Timestamp.valueOf(collectTime));
            pstmt.setString(3, filePath);

            int result = pstmt.executeUpdate();
            logger.info("插入数据记录成功: {}", fileName);
            return result > 0;

        } catch (SQLException e) {
            logger.error("插入数据记录失败: {}", fileName, e);
            return false;
        }
    }

    /**
     * 分页查询数据记录
     */
    public List<DataRecord> getRecords(int page, int pageSize) {
        List<DataRecord> records = new ArrayList<>();
        int offset = (page - 1) * pageSize;

        String sql = "SELECT id, file_name, collect_time, file_path FROM " + TABLE_NAME +
                " ORDER BY collect_time DESC LIMIT ? OFFSET ?";

        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, offset);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    DataRecord record = new DataRecord(
                            rs.getInt("id"),
                            rs.getString("file_name"),
                            rs.getTimestamp("collect_time").toLocalDateTime(),
                            rs.getString("file_path")
                    );
                    records.add(record);
                }
            }

            logger.debug("查询数据记录成功，页码: {}, 页大小: {}, 记录数: {}", page, pageSize, records.size());

        } catch (SQLException e) {
            logger.error("查询数据记录失败", e);
        }

        return records;
    }

    /**
     * 获取总记录数
     */
    public int getTotalCount() {
        String sql = "SELECT COUNT(*) FROM " + TABLE_NAME;

        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            logger.error("获取总记录数失败", e);
        }

        return 0;
    }

    /**
     * 删除数据记录
     */
    public boolean deleteRecord(int id) {
        String sql = "DELETE FROM " + TABLE_NAME + " WHERE id = ?";

        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);
            int result = pstmt.executeUpdate();

            logger.info("删除数据记录成功，ID: {}", id);
            return result > 0;

        } catch (SQLException e) {
            logger.error("删除数据记录失败，ID: {}", id, e);
            return false;
        }
    }

    /**
     * 获取数据库文件路径
     */
    public String getDbPath() {
        return dbPath;
    }

    /**
     * 创建Excel数据相关表（如果不存在）
     */
    private void createExcelDataTablesIfNotExists(Connection conn) throws SQLException {
        // 创建设备检测数据主表
        createDeviceDetectionDataTable(conn);

        // 创建基础字段表
        createDeviceDetectionBasicFieldTable(conn);

        // 创建表头表
        createDeviceDetectionTableHeaderTable(conn);

        // 创建表格数据表
        createDeviceDetectionTableDataTable(conn);
    }

    /**
     * 创建设备检测数据主表
     */
    private void createDeviceDetectionDataTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_DATA_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "device_code TEXT," +
                "template_id INTEGER," +
                "template_name TEXT," +
                "file_name TEXT NOT NULL," +
                "file_path TEXT NOT NULL," +
                "file_size INTEGER," +
                "parse_status INTEGER DEFAULT 0," + // 0-待解析，1-解析成功，2-解析失败
                "parse_message TEXT," +
                "parse_time TIMESTAMP," +
                "total_sheets INTEGER DEFAULT 0," +
                "parsed_sheets INTEGER DEFAULT 0," +
                "basic_fields_count INTEGER DEFAULT 0," +
                "table_rows_count INTEGER DEFAULT 0," +
                "create_by TEXT," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "update_by TEXT," +
                "update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "remark TEXT" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("设备检测数据主表创建成功: {}", DEVICE_DETECTION_DATA_TABLE);
        }
    }

    /**
     * 创建基础字段表
     */
    private void createDeviceDetectionBasicFieldTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_BASIC_FIELD_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "field_code TEXT NOT NULL," +
                "field_name TEXT," +
                "field_value TEXT," +
                "field_type TEXT," +
                "row_index INTEGER," +
                "col_index INTEGER," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("基础字段表创建成功: {}", DEVICE_DETECTION_BASIC_FIELD_TABLE);
        }
    }

    /**
     * 创建表头表
     */
    private void createDeviceDetectionTableHeaderTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_TABLE_HEADER_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "field_code TEXT NOT NULL," +
                "field_name TEXT," +
                "col_index INTEGER," +
                "data_type TEXT," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("表头表创建成功: {}", DEVICE_DETECTION_TABLE_HEADER_TABLE);
        }
    }

    /**
     * 创建表格数据表
     */
    private void createDeviceDetectionTableDataTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_TABLE_DATA_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "row_index INTEGER," +
                "field_code TEXT NOT NULL," +
                "field_value TEXT," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("表格数据表创建成功: {}", DEVICE_DETECTION_TABLE_DATA_TABLE);
        }
    }

    /**
     * 保存Excel解析结果到数据库
     *
     * @param parsingResult Excel解析结果
     * @return 保存是否成功
     */
    public boolean saveExcelParsingResult(ExcelParsingService.ExcelParsingResult parsingResult) {
        if (parsingResult == null || !parsingResult.isSuccess()) {
            logger.warn("Excel解析结果为空或解析失败，跳过保存");
            return false;
        }

        Connection conn = null;
        try {
            conn = getConnection();
            conn.setAutoCommit(false); // 开启事务

            // 1. 保存主记录
            Long detectionDataId = saveDetectionDataRecord(conn, parsingResult);
            if (detectionDataId == null) {
                conn.rollback();
                return false;
            }

            // 2. 保存Sheet数据
            for (ExcelParsingService.SheetParsingResult sheetResult : parsingResult.getSheetResults()) {
                if (!sheetResult.isSuccess()) {
                    logger.warn("跳过失败的Sheet: {}", sheetResult.getSheetName());
                    continue;
                }

                // 保存基础字段
                if (!saveBasicFields(conn, detectionDataId, sheetResult)) {
                    logger.error("保存基础字段失败，Sheet: {}", sheetResult.getSheetName());
                    conn.rollback();
                    return false;
                }

                // 保存表格数据
                if (!saveTableData(conn, detectionDataId, sheetResult)) {
                    logger.error("保存表格数据失败，Sheet: {}", sheetResult.getSheetName());
                    conn.rollback();
                    return false;
                }
            }

            // 3. 更新统计信息
            updateDetectionDataStatistics(conn, detectionDataId, parsingResult);

            conn.commit(); // 提交事务
            logger.info("Excel解析结果保存成功，主记录ID: {}", detectionDataId);
            return true;

        } catch (Exception e) {
            logger.error("保存Excel解析结果失败", e);
            try {
                if (conn != null) {
                    conn.rollback();
                }
            } catch (SQLException rollbackEx) {
                logger.error("事务回滚失败", rollbackEx);
            }
            return false;
        } finally {
            try {
                if (conn != null) {
                    conn.setAutoCommit(true);
                    conn.close();
                }
            } catch (SQLException e) {
                logger.error("关闭数据库连接失败", e);
            }
        }
    }

    /**
     * 保存检测数据主记录
     */
    private Long saveDetectionDataRecord(Connection conn, ExcelParsingService.ExcelParsingResult parsingResult) throws SQLException {
        String sql = "INSERT INTO " + DEVICE_DETECTION_DATA_TABLE + " (" +
                "device_code, template_name, file_name, file_path, file_size, " +
                "parse_status, parse_message, parse_time, total_sheets, " +
                "create_by, remark) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            // 从文件路径中提取设备编码（简单实现）
            String deviceCode = extractDeviceCodeFromFileName(parsingResult.getExcelFilePath());

            pstmt.setString(1, deviceCode);
            pstmt.setString(2, parsingResult.getTemplateName());
            pstmt.setString(3, new File(parsingResult.getExcelFilePath()).getName());
            pstmt.setString(4, parsingResult.getExcelFilePath());
            pstmt.setLong(5, getFileSize(parsingResult.getExcelFilePath()));
            pstmt.setInt(6, 1); // 解析成功
            pstmt.setString(7, "解析成功");
            pstmt.setTimestamp(8, Timestamp.valueOf(parsingResult.getParseTime()));
            pstmt.setInt(9, parsingResult.getSheetResults().size());
            pstmt.setString(10, "system");
            pstmt.setString(11, "Excel文件解析导入，模板: " + parsingResult.getTemplateCode());

            int result = pstmt.executeUpdate();
            if (result > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        return rs.getLong(1);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 保存基础字段数据
     */
    private boolean saveBasicFields(Connection conn, Long detectionDataId,
                                    ExcelParsingService.SheetParsingResult sheetResult) throws SQLException {
        if (sheetResult.getBasicFields().isEmpty()) {
            return true; // 没有基础字段，直接返回成功
        }

        String sql = "INSERT INTO " + DEVICE_DETECTION_BASIC_FIELD_TABLE + " (" +
                "detection_data_id, sheet_id, sheet_name, field_code, field_value) " +
                "VALUES (?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            for (Map.Entry<String, Object> entry : sheetResult.getBasicFields().entrySet()) {
                pstmt.setLong(1, detectionDataId);
                pstmt.setString(2, sheetResult.getSheetId());
                pstmt.setString(3, sheetResult.getSheetName());
                pstmt.setString(4, entry.getKey());
                pstmt.setString(5, entry.getValue() != null ? entry.getValue().toString() : "");
                pstmt.addBatch();
            }

            int[] results = pstmt.executeBatch();
            logger.debug("保存基础字段完成，Sheet: {}, 字段数: {}", sheetResult.getSheetName(), results.length);
            return true;
        }
    }

    /**
     * 保存表格数据
     */
    private boolean saveTableData(Connection conn, Long detectionDataId,
                                  ExcelParsingService.SheetParsingResult sheetResult) throws SQLException {
        if (sheetResult.getTableData().isEmpty()) {
            return true; // 没有表格数据，直接返回成功
        }

        String sql = "INSERT INTO " + DEVICE_DETECTION_TABLE_DATA_TABLE + " (" +
                "detection_data_id, sheet_id, sheet_name, row_index, field_code, field_value) " +
                "VALUES (?, ?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            int rowIndex = 0;
            for (Map<String, Object> rowData : sheetResult.getTableData()) {
                for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                    pstmt.setLong(1, detectionDataId);
                    pstmt.setString(2, sheetResult.getSheetId());
                    pstmt.setString(3, sheetResult.getSheetName());
                    pstmt.setInt(4, rowIndex);
                    pstmt.setString(5, entry.getKey());
                    pstmt.setString(6, entry.getValue() != null ? entry.getValue().toString() : "");
                    pstmt.addBatch();
                }
                rowIndex++;
            }

            int[] results = pstmt.executeBatch();
            logger.debug("保存表格数据完成，Sheet: {}, 数据行数: {}, 总字段数: {}",
                    sheetResult.getSheetName(), sheetResult.getTableData().size(), results.length);
            return true;
        }
    }

    /**
     * 更新检测数据统计信息
     */
    private void updateDetectionDataStatistics(Connection conn, Long detectionDataId,
                                               ExcelParsingService.ExcelParsingResult parsingResult) throws SQLException {
        int totalBasicFields = parsingResult.getSheetResults().stream()
                .mapToInt(s -> s.getBasicFields().size()).sum();
        int totalTableRows = parsingResult.getSheetResults().stream()
                .mapToInt(s -> s.getTableData().size()).sum();
        int parsedSheets = (int) parsingResult.getSheetResults().stream()
                .filter(ExcelParsingService.SheetParsingResult::isSuccess).count();

        String sql = "UPDATE " + DEVICE_DETECTION_DATA_TABLE + " SET " +
                "parsed_sheets = ?, basic_fields_count = ?, table_rows_count = ?, " +
                "update_time = CURRENT_TIMESTAMP WHERE id = ?";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, parsedSheets);
            pstmt.setInt(2, totalBasicFields);
            pstmt.setInt(3, totalTableRows);
            pstmt.setLong(4, detectionDataId);

            pstmt.executeUpdate();
            logger.debug("更新统计信息完成，ID: {}, 解析Sheet数: {}, 基础字段数: {}, 表格行数: {}",
                    detectionDataId, parsedSheets, totalBasicFields, totalTableRows);
        }
    }

    /**
     * 从文件名中提取设备编码
     */
    private String extractDeviceCodeFromFileName(String filePath) {
        try {
            String fileName = new File(filePath).getName();
            // 移除文件扩展名
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                fileName = fileName.substring(0, dotIndex);
            }

            // 简单的设备编码提取逻辑，可以根据实际需求调整
            // 假设文件名格式为：设备编码_其他信息.xlsx
            int underscoreIndex = fileName.indexOf('_');
            if (underscoreIndex > 0) {
                return fileName.substring(0, underscoreIndex);
            }

            return fileName; // 如果没有下划线，直接使用文件名作为设备编码
        } catch (Exception e) {
            logger.warn("提取设备编码失败，使用默认值: {}", filePath, e);
            return "UNKNOWN";
        }
    }

    /**
     * 获取文件大小
     */
    private long getFileSize(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists() ? file.length() : 0;
        } catch (Exception e) {
            logger.warn("获取文件大小失败: {}", filePath, e);
            return 0;
        }
    }

    /**
     * 查询Excel解析记录
     */
    public List<ExcelDataRecord> getExcelDataRecords(int page, int pageSize) {
        List<ExcelDataRecord> records = new ArrayList<>();
        int offset = (page - 1) * pageSize;

        String sql = "SELECT id, device_code, template_name, file_name, file_path, " +
                "parse_status, parse_message, parse_time, total_sheets, parsed_sheets, " +
                "basic_fields_count, table_rows_count, create_time, remark " +
                "FROM " + DEVICE_DETECTION_DATA_TABLE + " " +
                "ORDER BY create_time DESC LIMIT ? OFFSET ?";

        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, pageSize);
            pstmt.setInt(2, offset);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    ExcelDataRecord record = new ExcelDataRecord();
                    record.setId(rs.getLong("id"));
                    record.setDeviceCode(rs.getString("device_code"));
                    record.setTemplateName(rs.getString("template_name"));
                    record.setFileName(rs.getString("file_name"));
                    record.setFilePath(rs.getString("file_path"));
                    record.setParseStatus(rs.getInt("parse_status"));
                    record.setParseMessage(rs.getString("parse_message"));

                    Timestamp parseTime = rs.getTimestamp("parse_time");
                    if (parseTime != null) {
                        record.setParseTime(parseTime.toLocalDateTime());
                    }

                    record.setTotalSheets(rs.getInt("total_sheets"));
                    record.setParsedSheets(rs.getInt("parsed_sheets"));
                    record.setBasicFieldsCount(rs.getInt("basic_fields_count"));
                    record.setTableRowsCount(rs.getInt("table_rows_count"));

                    Timestamp createTime = rs.getTimestamp("create_time");
                    if (createTime != null) {
                        record.setCreateTime(createTime.toLocalDateTime());
                    }

                    record.setRemark(rs.getString("remark"));
                    records.add(record);
                }
            }

            logger.debug("查询Excel数据记录成功，页码: {}, 页大小: {}, 记录数: {}", page, pageSize, records.size());

        } catch (SQLException e) {
            logger.error("查询Excel数据记录失败", e);
        }

        return records;
    }

    /**
     * 获取Excel数据记录总数
     */
    public int getExcelDataRecordCount() {
        String sql = "SELECT COUNT(*) FROM " + DEVICE_DETECTION_DATA_TABLE;

        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            logger.error("获取Excel数据记录总数失败", e);
        }

        return 0;
    }

    /**
     * 获取Excel数据记录的详细信息，包括基础字段和表格数据
     */
    public ExcelDataDetail getExcelDataDetail(Long detectionDataId) {
        ExcelDataDetail detail = new ExcelDataDetail();

        try (Connection conn = getConnection()) {
            // 1. 获取主记录信息
            ExcelDataRecord mainRecord = getExcelDataRecordById(conn, detectionDataId);
            if (mainRecord == null) {
                logger.warn("未找到ID为{}的Excel数据记录", detectionDataId);
                return null;
            }
            detail.setMainRecord(mainRecord);

            // 2. 获取基础字段数据
            List<BasicFieldData> basicFields = getBasicFieldsByDetectionId(conn, detectionDataId);
            detail.setBasicFields(basicFields);

            // 3. 获取表格数据
            List<TableDataInfo> tableData = getTableDataByDetectionId(conn, detectionDataId);
            detail.setTableData(tableData);

            logger.info("获取Excel数据详情成功，ID: {}, 基础字段数: {}, 表格数据行数: {}",
                    detectionDataId, basicFields.size(), tableData.size());

        } catch (SQLException e) {
            logger.error("获取Excel数据详情失败，ID: {}", detectionDataId, e);
            return null;
        }

        return detail;
    }

    /**
     * 根据ID获取Excel数据记录
     */
    private ExcelDataRecord getExcelDataRecordById(Connection conn, Long id) throws SQLException {
        String sql = "SELECT id, device_code, template_name, file_name, file_path, file_size, " +
                "parse_status, parse_message, parse_time, total_sheets, parsed_sheets, " +
                "basic_fields_count, table_rows_count, create_time, remark " +
                "FROM " + DEVICE_DETECTION_DATA_TABLE + " WHERE id = ?";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    ExcelDataRecord record = new ExcelDataRecord();
                    record.setId(rs.getLong("id"));
                    record.setDeviceCode(rs.getString("device_code"));
                    record.setTemplateName(rs.getString("template_name"));
                    record.setFileName(rs.getString("file_name"));
                    record.setFilePath(rs.getString("file_path"));
                    record.setFileSize(rs.getLong("file_size"));
                    record.setParseStatus(rs.getInt("parse_status"));
                    record.setParseMessage(rs.getString("parse_message"));

                    Timestamp parseTime = rs.getTimestamp("parse_time");
                    if (parseTime != null) {
                        record.setParseTime(parseTime.toLocalDateTime());
                    }

                    record.setTotalSheets(rs.getInt("total_sheets"));
                    record.setParsedSheets(rs.getInt("parsed_sheets"));
                    record.setBasicFieldsCount(rs.getInt("basic_fields_count"));
                    record.setTableRowsCount(rs.getInt("table_rows_count"));

                    Timestamp createTime = rs.getTimestamp("create_time");
                    if (createTime != null) {
                        record.setCreateTime(createTime.toLocalDateTime());
                    }

                    record.setRemark(rs.getString("remark"));
                    return record;
                }
            }
        }

        return null;
    }

    /**
     * 获取基础字段数据
     */
    private List<BasicFieldData> getBasicFieldsByDetectionId(Connection conn, Long detectionDataId) throws SQLException {
        List<BasicFieldData> basicFields = new ArrayList<>();

        String sql = "SELECT sheet_id, sheet_name, field_code, field_name, field_value, " +
                "field_type, row_index, col_index " +
                "FROM " + DEVICE_DETECTION_BASIC_FIELD_TABLE + " " +
                "WHERE detection_data_id = ? ORDER BY sheet_id, field_code";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, detectionDataId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    BasicFieldData field = new BasicFieldData();
                    field.setSheetId(rs.getString("sheet_id"));
                    field.setSheetName(rs.getString("sheet_name"));
                    field.setFieldCode(rs.getString("field_code"));
                    field.setFieldName(rs.getString("field_name"));
                    field.setFieldValue(rs.getString("field_value"));
                    field.setFieldType(rs.getString("field_type"));
                    field.setRowIndex(rs.getInt("row_index"));
                    field.setColIndex(rs.getInt("col_index"));
                    basicFields.add(field);
                }
            }
        }

        return basicFields;
    }

    /**
     * 获取表格数据
     */
    private List<TableDataInfo> getTableDataByDetectionId(Connection conn, Long detectionDataId) throws SQLException {
        List<TableDataInfo> tableDataList = new ArrayList<>();

        String sql = "SELECT sheet_id, sheet_name, row_index, field_code, field_value " +
                "FROM " + DEVICE_DETECTION_TABLE_DATA_TABLE + " " +
                "WHERE detection_data_id = ? ORDER BY sheet_id, row_index, field_code";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, detectionDataId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    TableDataInfo data = new TableDataInfo();
                    data.setSheetId(rs.getString("sheet_id"));
                    data.setSheetName(rs.getString("sheet_name"));
                    data.setRowIndex(rs.getInt("row_index"));
                    data.setFieldCode(rs.getString("field_code"));
                    data.setFieldValue(rs.getString("field_value"));
                    tableDataList.add(data);
                }
            }
        }

        return tableDataList;
    }

    /**
     * Excel数据记录类
     */
    public static class ExcelDataRecord {
        private Long id;
        private String deviceCode;
        private String templateName;
        private String fileName;
        private String filePath;
        private Long fileSize;
        private Integer parseStatus;
        private String parseMessage;
        private LocalDateTime parseTime;
        private Integer totalSheets;
        private Integer parsedSheets;
        private Integer basicFieldsCount;
        private Integer tableRowsCount;
        private LocalDateTime createTime;
        private String remark;

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getDeviceCode() {
            return deviceCode;
        }

        public void setDeviceCode(String deviceCode) {
            this.deviceCode = deviceCode;
        }

        public String getTemplateName() {
            return templateName;
        }

        public void setTemplateName(String templateName) {
            this.templateName = templateName;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public Integer getParseStatus() {
            return parseStatus;
        }

        public void setParseStatus(Integer parseStatus) {
            this.parseStatus = parseStatus;
        }

        public String getParseMessage() {
            return parseMessage;
        }

        public void setParseMessage(String parseMessage) {
            this.parseMessage = parseMessage;
        }

        public LocalDateTime getParseTime() {
            return parseTime;
        }

        public void setParseTime(LocalDateTime parseTime) {
            this.parseTime = parseTime;
        }

        public Integer getTotalSheets() {
            return totalSheets;
        }

        public void setTotalSheets(Integer totalSheets) {
            this.totalSheets = totalSheets;
        }

        public Integer getParsedSheets() {
            return parsedSheets;
        }

        public void setParsedSheets(Integer parsedSheets) {
            this.parsedSheets = parsedSheets;
        }

        public Integer getBasicFieldsCount() {
            return basicFieldsCount;
        }

        public void setBasicFieldsCount(Integer basicFieldsCount) {
            this.basicFieldsCount = basicFieldsCount;
        }

        public Integer getTableRowsCount() {
            return tableRowsCount;
        }

        public void setTableRowsCount(Integer tableRowsCount) {
            this.tableRowsCount = tableRowsCount;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getParseStatusText() {
            if (parseStatus == null) return "未知";
            switch (parseStatus) {
                case 0:
                    return "待解析";
                case 1:
                    return "解析成功";
                case 2:
                    return "解析失败";
                default:
                    return "未知";
            }
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }
    }

    /**
     * Excel数据详情类
     */
    public static class ExcelDataDetail {
        private ExcelDataRecord mainRecord;
        private List<BasicFieldData> basicFields;
        private List<TableDataInfo> tableData;

        public ExcelDataRecord getMainRecord() {
            return mainRecord;
        }

        public void setMainRecord(ExcelDataRecord mainRecord) {
            this.mainRecord = mainRecord;
        }

        public List<BasicFieldData> getBasicFields() {
            return basicFields;
        }

        public void setBasicFields(List<BasicFieldData> basicFields) {
            this.basicFields = basicFields;
        }

        public List<TableDataInfo> getTableData() {
            return tableData;
        }

        public void setTableData(List<TableDataInfo> tableData) {
            this.tableData = tableData;
        }
    }

    /**
     * 基础字段数据类
     */
    public static class BasicFieldData {
        private String sheetId;
        private String sheetName;
        private String fieldCode;
        private String fieldName;
        private String fieldValue;
        private String fieldType;
        private Integer rowIndex;
        private Integer colIndex;

        // Getters and Setters
        public String getSheetId() { return sheetId; }
        public void setSheetId(String sheetId) { this.sheetId = sheetId; }

        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }

        public String getFieldCode() { return fieldCode; }
        public void setFieldCode(String fieldCode) { this.fieldCode = fieldCode; }

        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldValue() { return fieldValue; }
        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public Integer getRowIndex() { return rowIndex; }
        public void setRowIndex(Integer rowIndex) { this.rowIndex = rowIndex; }

        public Integer getColIndex() { return colIndex; }
        public void setColIndex(Integer colIndex) { this.colIndex = colIndex; }
    }

    /**
     * 表格数据信息类
     */
    public static class TableDataInfo {
        private String sheetId;
        private String sheetName;
        private Integer rowIndex;
        private String fieldCode;
        private String fieldValue;

        // Getters and Setters
        public String getSheetId() { return sheetId; }
        public void setSheetId(String sheetId) { this.sheetId = sheetId; }

        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }

        public Integer getRowIndex() { return rowIndex; }
        public void setRowIndex(Integer rowIndex) { this.rowIndex = rowIndex; }

        public String getFieldCode() { return fieldCode; }
        public void setFieldCode(String fieldCode) { this.fieldCode = fieldCode; }

        public String getFieldValue() { return fieldValue; }
        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }
    }
}
